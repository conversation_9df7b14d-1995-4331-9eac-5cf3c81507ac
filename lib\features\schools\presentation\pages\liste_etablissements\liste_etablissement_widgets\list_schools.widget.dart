import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_state.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'dart:convert'; // Import for base64 decoding
import 'alert.widget.dart';


class ListeEtablissementUtilisateurWidget extends StatelessWidget {
  final List<EtablissementUtilisateur> userSchools; // Accept list of user schools

  const ListeEtablissementUtilisateurWidget({super.key, required this.userSchools});

  @override
  Widget build(BuildContext context) {
    return BlocListener<SchoolsCubit, SchoolsState>(
      listener: (context, state) {
        if (state is FinancialStatusLoaded) {
          debugPrint('FinancialStatusLoaded: ${state.financialStatus.enRegle}');
          if (!state.financialStatus.enRegle) {
            // Show alert for unpaid fees
            showDialog(
              context: context,
              builder: (context) => AlertWidget(
                message: "Vous avez des frais impayés. Veuillez régulariser votre situation afin d'accèder á votre espace",
              ),
            );
          } else {
            // Navigate to dashboard - will be implemented in next phase
            // TODO: Navigate to dashboard page
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Accès autorisé - Navigation vers le dashboard à implémenter")),
            );
          }
        } else if (state is FinancialStatusError) {
          debugPrint('FinancialStatusError: ${state.message}');
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Erreur: ${state.message}")),
          );
        }
      },
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: userSchools.length, // Use the provided list
        itemBuilder: (context, index) {
          final school = userSchools[index];
          return Card(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: InkWell(
            onTap: () async {
              await _handleSchoolSelection(context, school);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 1.0),
              child: ListTile(
                leading: school.logoEtablissement.isNotEmpty
                    ? Image.memory(
                        base64Decode(school.logoEtablissement),
                        width: 50, // Adjust size as needed
                        height: 50, // Adjust size as needed
                        fit: BoxFit.contain,
                      )
                    : Image.asset(
                        "assets/images/logo_iam.png",
                        width: 50, // Adjust size as needed
                        height: 50, // Adjust size as needed
                        fit: BoxFit.contain,
                      ),
                title: Text(school.libelleEtab), // Display school name
               subtitle: Text.rich(
                 TextSpan(
                   children: [
                     const TextSpan(
                       text: "PROFILE: ",
                       style: TextStyle(fontWeight: FontWeight.bold),
                     ),
                     TextSpan(text: "${school.profil == 'ETU' ? 'ÉTUDIANT': 
                                       school.profil == 'PAR'? 'PARENT' : 
                                       school.profil == 'TUT' ? 'TUTEUR' : 'ÉTUDIANT'} "), // Display user profile
                     const TextSpan(
                       text: "ID: ",
                       style: TextStyle(fontWeight: FontWeight.bold),
                     ),
                     TextSpan(text: school.codeUtilisateur), // Display school ID
                   ],
                 ),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 10),
              ),
            ),
          ),
        );
      },
      ),
    );
  }

  /// Handle school selection and check financial status
  Future<void> _handleSchoolSelection(BuildContext context, EtablissementUtilisateur school) async {
    try {
      // Get phone number from SharedPreferences
      final authLocalDataSource = sl<AuthLocalDataSource>();
      final phoneNumber = await authLocalDataSource.getPhoneNumber();

      if (!context.mounted) return;

      if (phoneNumber != null) {
        // Trigger financial status check via BLoC
        context.read<SchoolsCubit>().checkFinancialStatus(
          codeEtab: school.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: school.codeUtilisateur,
        );
      } else {
        // Handle case where phone number is not available
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: Numéro de téléphone non disponible")),
        );
      }
    } catch (e) {
      // Handle any errors
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: $e")),
        );
      }
    }
  }
}