import 'package:flutter/material.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'dart:convert'; // Import for base64 decoding


class ListeEtablissementUtilisateurWidget extends StatelessWidget {
  final List<EtablissementUtilisateur> userSchools; // Accept list of user schools

  const ListeEtablissementUtilisateurWidget({super.key, required this.userSchools});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: userSchools.length, // Use the provided list
      itemBuilder: (context, index) {
        final school = userSchools[index];
        return Card(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: InkWell(
            onTap: () {
              // TODO: Implement navigation based on school profile
              // if (school.profile == "PARENT" || school.profile == "TUTEUR") {
              //   Navigator.pushNamed(context, "/dossier_selection");
              // } else {
              //   Navigator.pushNamed(context, "/dashboard");
              // }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 1.0),
              child: ListTile(
                leading: school.logoEtablissement.isNotEmpty
                    ? Image.memory(
                        base64Decode(school.logoEtablissement),
                        width: 50, // Adjust size as needed
                        height: 50, // Adjust size as needed
                        fit: BoxFit.contain,
                      )
                    : Image.asset(
                        "assets/images/logo_iam.png",
                        width: 50, // Adjust size as needed
                        height: 50, // Adjust size as needed
                        fit: BoxFit.contain,
                      ),
                title: Text(school.libelleEtab), // Display school name
               subtitle: Text.rich(
                 TextSpan(
                   children: [
                     const TextSpan(
                       text: "PROFILE: ",
                       style: TextStyle(fontWeight: FontWeight.bold),
                     ),
                     TextSpan(text: "${school.profil == 'ETU' ? 'ÉTUDIANT': 
                                       school.profil == 'PAR'? 'PARENT' : 
                                       school.profil == 'TUT' ? 'TUTEUR' : 'ÉTUDIANT'} "), // Display user profile
                     const TextSpan(
                       text: "ID: ",
                       style: TextStyle(fontWeight: FontWeight.bold),
                     ),
                     TextSpan(text: school.codeUtilisateur), // Display school ID
                   ],
                 ),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 10),
              ),
            ),
          ),
        );
      },
    );
  }
}