import '../../../../core/api/api_client.dart';
import '../models/dashboard_model.dart';

/// Abstract remote data source for dashboard operations
abstract class DashboardRemoteDataSource {
  /// Load dashboard data from remote API
  Future<DashboardModel> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
  });
}

/// Implementation of DashboardRemoteDataSource
class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiClient apiClient;

  DashboardRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<DashboardModel> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
  }) async {
    try {
      // Make API call with query parameters
      final response = await apiClient.getWithToken(
        '/dashboard',
        queryParameters: {
          'codeEtab': codeEtab,
          'telephone': telephone,
          'codeEtudiant': codeEtudiant,
        },
      );

      // Parse response and return model
      return DashboardModel.fromJson(response.data);
    } catch (e) {
      // Handle errors appropriately - convert to appropriate exceptions
      throw Exception('Failed to load dashboard data: $e');
    }
  }
}
